import React from 'react';
import { useParams } from 'react-router-dom';

const FlipbookViewer: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Flipbook Viewer
        </h1>
        <p className="text-gray-600 dark:text-gray-300">
          Viewing flipbook with ID: {id}
        </p>
        
        {/* Placeholder for flipbook viewer */}
        <div className="mt-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center">
          <p className="text-gray-500 dark:text-gray-400">
            Flipbook viewer will be implemented in the next phase
          </p>
        </div>
      </div>
    </div>
  );
};

export default FlipbookViewer;
