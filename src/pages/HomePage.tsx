import React, { useState } from 'react';
import { Upload, BookOpen, Smartphone, Globe, Download, Palette } from 'lucide-react';
import { useTrial } from '../contexts/TrialContext';
import PDFUploader from '../components/PDFUploader';
import TrialLimitModal from '../components/TrialLimitModal';

const HomePage: React.FC = () => {
  const { trialData, canCreateFlipbook, showUpgradePrompt } = useTrial();
  const [showTrialModal, setShowTrialModal] = useState(false);

  const handleCreateFlipbook = () => {
    if (!canCreateFlipbook) {
      setShowTrialModal(true);
      return;
    }
    // Proceed with flipbook creation
  };

  const features = [
    {
      icon: BookOpen,
      title: 'Realistic Page Flipping',
      description: 'Experience smooth, realistic page-turning animations that mimic real books.',
    },
    {
      icon: Smartphone,
      title: 'Mobile Friendly',
      description: 'Perfect viewing experience on desktop, tablet, and mobile devices.',
    },
    {
      icon: Globe,
      title: 'RTL Support',
      description: 'Full support for Arabic, Hindi, and other right-to-left languages.',
    },
    {
      icon: Download,
      title: 'Multiple Export Options',
      description: 'Share via link, embed code, or download as ZIP file.',
    },
    {
      icon: Palette,
      title: 'Customizable Design',
      description: 'Customize colors, add watermarks, and personalize your flipbook.',
    },
    {
      icon: Upload,
      title: 'Easy Upload',
      description: 'Simply drag and drop your PDF files up to 100MB.',
    },
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary/10 to-accent/10 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
              Transform PDFs into
              <span className="text-primary"> Interactive Flipbooks</span>
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
              Create stunning, mobile-friendly flipbooks from your PDFs with realistic page-turning effects. 
              Perfect for ebooks, catalogs, magazines, and presentations.
            </p>
            
            {/* Trial Status */}
            <div className="mb-8">
              <div className="inline-flex items-center bg-white dark:bg-gray-800 rounded-full px-6 py-3 shadow-lg">
                <span className="text-sm text-gray-600 dark:text-gray-300">
                  Free Trial: {trialData.flipbooksCreated}/{trialData.maxFlipbooks} flipbooks used
                </span>
                {!canCreateFlipbook && (
                  <button
                    onClick={showUpgradePrompt}
                    className="ml-4 bg-accent text-gray-900 px-4 py-1 rounded-full text-sm font-medium hover:bg-accent/90 transition-colors"
                  >
                    Upgrade Now
                  </button>
                )}
              </div>
            </div>

            {/* Upload Section */}
            <div className="max-w-2xl mx-auto">
              <PDFUploader onUploadStart={handleCreateFlipbook} />
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-white dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Everything You Need
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Powerful features to create professional flipbooks that engage your audience.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className="bg-gray-50 dark:bg-gray-800 p-6 rounded-xl hover:shadow-lg transition-shadow"
              >
                <div className="bg-primary/10 p-3 rounded-lg w-fit mb-4">
                  <feature.icon className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Simple Pricing
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              Start free, upgrade when you need more.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {/* Free Trial */}
            <div className="bg-white dark:bg-gray-900 p-8 rounded-xl shadow-lg">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                Free Trial
              </h3>
              <div className="text-4xl font-bold text-primary mb-6">
                $0
              </div>
              <ul className="space-y-3 mb-8">
                <li className="flex items-center text-gray-600 dark:text-gray-300">
                  <span className="text-green-500 mr-2">✓</span>
                  2 flipbooks
                </li>
                <li className="flex items-center text-gray-600 dark:text-gray-300">
                  <span className="text-green-500 mr-2">✓</span>
                  10MB file limit
                </li>
                <li className="flex items-center text-gray-600 dark:text-gray-300">
                  <span className="text-green-500 mr-2">✓</span>
                  7 days expiry
                </li>
                <li className="flex items-center text-gray-400">
                  <span className="text-red-500 mr-2">✗</span>
                  No export/download
                </li>
              </ul>
              <button className="w-full btn-secondary">
                Current Plan
              </button>
            </div>

            {/* Pro Plan */}
            <div className="bg-primary text-white p-8 rounded-xl shadow-lg relative">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-accent text-gray-900 px-4 py-1 rounded-full text-sm font-medium">
                  Most Popular
                </span>
              </div>
              <h3 className="text-2xl font-bold mb-4">
                Pro Plan
              </h3>
              <div className="text-4xl font-bold mb-6">
                $19<span className="text-lg">/month</span>
              </div>
              <ul className="space-y-3 mb-8">
                <li className="flex items-center">
                  <span className="text-green-400 mr-2">✓</span>
                  Unlimited flipbooks
                </li>
                <li className="flex items-center">
                  <span className="text-green-400 mr-2">✓</span>
                  100MB file limit
                </li>
                <li className="flex items-center">
                  <span className="text-green-400 mr-2">✓</span>
                  No expiry
                </li>
                <li className="flex items-center">
                  <span className="text-green-400 mr-2">✓</span>
                  Full export options
                </li>
                <li className="flex items-center">
                  <span className="text-green-400 mr-2">✓</span>
                  Custom branding
                </li>
              </ul>
              <button className="w-full bg-white text-primary font-medium px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors">
                Upgrade Now
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Trial Limit Modal */}
      {showTrialModal && (
        <TrialLimitModal
          isOpen={showTrialModal}
          onClose={() => setShowTrialModal(false)}
        />
      )}
    </div>
  );
};

export default HomePage;
