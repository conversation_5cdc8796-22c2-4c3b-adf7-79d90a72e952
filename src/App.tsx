import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider } from './contexts/ThemeContext';
import { TrialProvider } from './contexts/TrialContext';
import HomePage from './pages/HomePage';
import FlipbookViewer from './pages/FlipbookViewer';
import Header from './components/Header';
import Footer from './components/Footer';

function App() {
  return (
    <ThemeProvider>
      <TrialProvider>
        <Router>
          <div className="min-h-screen bg-background text-foreground">
            <Header />
            <main className="flex-1">
              <Routes>
                <Route path="/" element={<HomePage />} />
                <Route path="/flipbook/:id" element={<FlipbookViewer />} />
              </Routes>
            </main>
            <Footer />
          </div>
        </Router>
      </TrialProvider>
    </ThemeProvider>
  );
}

export default App;
