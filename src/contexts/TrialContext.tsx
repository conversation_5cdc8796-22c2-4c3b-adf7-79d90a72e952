import React, { createContext, useContext, useEffect, useState } from 'react';

interface TrialData {
  flipbooksCreated: number;
  maxFlipbooks: number;
  maxFileSize: number; // in MB
  expiryDays: number;
  hasAccount: boolean;
  email?: string;
  createdAt: string;
}

interface TrialContextType {
  trialData: TrialData;
  canCreateFlipbook: boolean;
  canExport: boolean;
  createFlipbook: () => void;
  createAccount: (email: string) => void;
  showUpgradePrompt: () => void;
}

const TrialContext = createContext<TrialContextType | undefined>(undefined);

export const useTrial = () => {
  const context = useContext(TrialContext);
  if (context === undefined) {
    throw new Error('useTrial must be used within a TrialProvider');
  }
  return context;
};

interface TrialProviderProps {
  children: React.ReactNode;
}

const DEFAULT_TRIAL_DATA: TrialData = {
  flipbooksCreated: 0,
  maxFlipbooks: 2,
  maxFileSize: 10, // 10MB for trial
  expiryDays: 7,
  hasAccount: false,
  createdAt: new Date().toISOString(),
};

export const TrialProvider: React.FC<TrialProviderProps> = ({ children }) => {
  const [trialData, setTrialData] = useState<TrialData>(() => {
    const saved = localStorage.getItem('flipnest-trial');
    if (saved) {
      return JSON.parse(saved);
    }
    return DEFAULT_TRIAL_DATA;
  });

  useEffect(() => {
    localStorage.setItem('flipnest-trial', JSON.stringify(trialData));
  }, [trialData]);

  const canCreateFlipbook = trialData.flipbooksCreated < trialData.maxFlipbooks;
  const canExport = false; // Trial users cannot export

  const createFlipbook = () => {
    if (canCreateFlipbook) {
      setTrialData(prev => ({
        ...prev,
        flipbooksCreated: prev.flipbooksCreated + 1,
      }));
    }
  };

  const createAccount = (email: string) => {
    setTrialData(prev => ({
      ...prev,
      hasAccount: true,
      email,
    }));
  };

  const showUpgradePrompt = () => {
    // This would typically show a modal or redirect to pricing
    console.log('Show upgrade prompt');
  };

  return (
    <TrialContext.Provider
      value={{
        trialData,
        canCreateFlipbook,
        canExport,
        createFlipbook,
        createAccount,
        showUpgradePrompt,
      }}
    >
      {children}
    </TrialContext.Provider>
  );
};
