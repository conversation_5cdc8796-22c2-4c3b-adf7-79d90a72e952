import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON>, Sun, BookOpen } from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';
import { useTrial } from '../contexts/TrialContext';

const Header: React.FC = () => {
  const { theme, toggleTheme } = useTheme();
  const { trialData, canCreateFlipbook } = useTrial();

  return (
    <header className="bg-white dark:bg-gray-900 shadow-sm border-b border-gray-200 dark:border-gray-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2">
            <div className="bg-primary p-2 rounded-lg">
              <BookOpen className="h-6 w-6 text-white" />
            </div>
            <span className="text-xl font-bold text-gray-900 dark:text-white">
              FlipNest
            </span>
          </Link>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link
              to="/"
              className="text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors"
            >
              Home
            </Link>
            <a
              href="#features"
              className="text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors"
            >
              Features
            </a>
            <a
              href="#pricing"
              className="text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors"
            >
              Pricing
            </a>
          </nav>

          {/* Right side */}
          <div className="flex items-center space-x-4">
            {/* Trial Status */}
            <div className="hidden sm:flex items-center space-x-2 text-sm">
              <span className="text-gray-600 dark:text-gray-300">
                Trial: {trialData.flipbooksCreated}/{trialData.maxFlipbooks}
              </span>
              {!canCreateFlipbook && (
                <span className="bg-accent text-gray-900 px-2 py-1 rounded text-xs font-medium">
                  Upgrade
                </span>
              )}
            </div>

            {/* Theme Toggle */}
            <button
              onClick={toggleTheme}
              className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
              aria-label="Toggle theme"
            >
              {theme === 'light' ? (
                <Moon className="h-5 w-5 text-gray-600 dark:text-gray-300" />
              ) : (
                <Sun className="h-5 w-5 text-gray-600 dark:text-gray-300" />
              )}
            </button>

            {/* CTA Button */}
            <Link
              to="/"
              className="btn-primary"
            >
              Create Flipbook
            </Link>
          </div>
        </div>
      </div>

      {/* Google AdSense Placeholder - Top Banner */}
      {/* AdSense Top Banner 728x90 */}
    </header>
  );
};

export default Header;
