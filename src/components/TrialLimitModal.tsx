import React, { useState } from 'react';
import { X, Crown, Mail, CreditCard } from 'lucide-react';
import { useTrial } from '../contexts/TrialContext';

interface TrialLimitModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const TrialLimitModal: React.FC<TrialLimitModalProps> = ({ isOpen, onClose }) => {
  const [showEmailForm, setShowEmailForm] = useState(false);
  const [email, setEmail] = useState('');
  const { trialData, createAccount } = useTrial();

  if (!isOpen) return null;

  const handleEmailSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (email) {
      createAccount(email);
      // Here you would typically send the email to your backend
      console.log('Account created with email:', email);
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-900 rounded-xl max-w-md w-full p-6 relative">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          <X className="h-6 w-6" />
        </button>

        {/* Header */}
        <div className="text-center mb-6">
          <div className="bg-accent/10 p-3 rounded-full w-fit mx-auto mb-4">
            <Crown className="h-8 w-8 text-accent" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Trial Limit Reached
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            You've used all {trialData.maxFlipbooks} of your free flipbooks. 
            Upgrade to continue creating amazing flipbooks!
          </p>
        </div>

        {!showEmailForm ? (
          <div className="space-y-4">
            {/* Upgrade Options */}
            <div className="border border-primary rounded-lg p-4 bg-primary/5">
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-semibold text-gray-900 dark:text-white">
                  Pro Plan
                </h3>
                <span className="text-2xl font-bold text-primary">
                  $19<span className="text-sm">/month</span>
                </span>
              </div>
              <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1 mb-4">
                <li>✓ Unlimited flipbooks</li>
                <li>✓ 100MB file limit</li>
                <li>✓ No expiry</li>
                <li>✓ Full export options</li>
                <li>✓ Custom branding</li>
              </ul>
              <button className="w-full btn-primary flex items-center justify-center">
                <CreditCard className="h-4 w-4 mr-2" />
                Upgrade Now
              </button>
            </div>

            {/* Email Option */}
            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                Or create a free account to track your trial
              </p>
              <button
                onClick={() => setShowEmailForm(true)}
                className="btn-secondary flex items-center justify-center mx-auto"
              >
                <Mail className="h-4 w-4 mr-2" />
                Create Free Account
              </button>
            </div>

            {/* Continue Without Account */}
            <div className="text-center pt-4 border-t border-gray-200 dark:border-gray-700">
              <button
                onClick={onClose}
                className="text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
              >
                Continue browsing
              </button>
            </div>
          </div>
        ) : (
          <form onSubmit={handleEmailSubmit} className="space-y-4">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Email Address
              </label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-800 dark:text-white"
                placeholder="Enter your email"
                required
              />
            </div>
            
            <div className="text-xs text-gray-500 dark:text-gray-400">
              We'll send you updates about your flipbooks and notify you when they're about to expire.
            </div>

            <div className="flex space-x-3">
              <button
                type="button"
                onClick={() => setShowEmailForm(false)}
                className="flex-1 btn-secondary"
              >
                Back
              </button>
              <button
                type="submit"
                className="flex-1 btn-primary"
              >
                Create Account
              </button>
            </div>
          </form>
        )}

        {/* Features Reminder */}
        <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <h4 className="font-medium text-gray-900 dark:text-white mb-2">
            What you get with Pro:
          </h4>
          <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
            <li>• Unlimited flipbook creation</li>
            <li>• Larger file uploads (100MB)</li>
            <li>• No expiration dates</li>
            <li>• Download and embed options</li>
            <li>• Remove FlipNest branding</li>
            <li>• Priority support</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default TrialLimitModal;
