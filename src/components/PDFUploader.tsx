import React, { useState, useCallback } from 'react';
import { Upload, FileText, AlertCircle, CheckCircle } from 'lucide-react';
import { useTrial } from '../contexts/TrialContext';

interface PDFUploaderProps {
  onUploadStart: () => void;
}

const PDFUploader: React.FC<PDFUploaderProps> = ({ onUploadStart }) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'uploading' | 'success' | 'error'>('idle');
  const [uploadProgress, setUploadProgress] = useState(0);
  const [errorMessage, setErrorMessage] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const { trialData, canCreateFlipbook } = useTrial();

  const validateFile = (file: File): string | null => {
    // Check file type
    if (file.type !== 'application/pdf') {
      return 'Please select a PDF file.';
    }

    // Check file size (convert MB to bytes)
    const maxSizeBytes = trialData.maxFileSize * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      return `File size must be less than ${trialData.maxFileSize}MB. Current file is ${(file.size / 1024 / 1024).toFixed(1)}MB.`;
    }

    return null;
  };

  const handleFileSelect = useCallback((file: File) => {
    const error = validateFile(file);
    if (error) {
      setErrorMessage(error);
      setUploadStatus('error');
      return;
    }

    setSelectedFile(file);
    setErrorMessage('');
    setUploadStatus('idle');
  }, [trialData.maxFileSize]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  const simulateUpload = async () => {
    setUploadStatus('uploading');
    setUploadProgress(0);

    // Simulate upload progress
    for (let i = 0; i <= 100; i += 10) {
      setUploadProgress(i);
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    setUploadStatus('success');
    
    // Simulate processing time
    setTimeout(() => {
      // Here you would typically redirect to the flipbook viewer
      console.log('Flipbook created successfully!');
    }, 1000);
  };

  const handleUpload = async () => {
    if (!selectedFile || !canCreateFlipbook) {
      onUploadStart(); // This will trigger the trial limit modal
      return;
    }

    try {
      await simulateUpload();
    } catch (error) {
      setUploadStatus('error');
      setErrorMessage('Upload failed. Please try again.');
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto">
      {/* Upload Area */}
      <div
        className={`upload-area ${isDragOver ? 'dragover' : ''} ${
          uploadStatus === 'error' ? 'border-red-300 bg-red-50 dark:border-red-600 dark:bg-red-900/20' : ''
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          type="file"
          accept=".pdf"
          onChange={handleFileInputChange}
          className="hidden"
          id="pdf-upload"
        />

        {uploadStatus === 'idle' && !selectedFile && (
          <div className="text-center">
            <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Upload your PDF
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Drag and drop your PDF file here, or click to browse
            </p>
            <label
              htmlFor="pdf-upload"
              className="btn-primary cursor-pointer inline-block"
            >
              Choose File
            </label>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
              Maximum file size: {trialData.maxFileSize}MB
            </p>
          </div>
        )}

        {selectedFile && uploadStatus === 'idle' && (
          <div className="text-center">
            <FileText className="h-12 w-12 text-primary mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {selectedFile.name}
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Size: {(selectedFile.size / 1024 / 1024).toFixed(1)}MB
            </p>
            <button
              onClick={handleUpload}
              className="btn-primary"
              disabled={!canCreateFlipbook}
            >
              {canCreateFlipbook ? 'Create Flipbook' : 'Trial Limit Reached'}
            </button>
          </div>
        )}

        {uploadStatus === 'uploading' && (
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Creating your flipbook...
            </h3>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-4">
              <div
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{ width: `${uploadProgress}%` }}
              ></div>
            </div>
            <p className="text-gray-600 dark:text-gray-300">
              {uploadProgress}% complete
            </p>
          </div>
        )}

        {uploadStatus === 'success' && (
          <div className="text-center">
            <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Flipbook created successfully!
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Your flipbook is ready to view and share.
            </p>
            <button className="btn-primary">
              View Flipbook
            </button>
          </div>
        )}

        {uploadStatus === 'error' && (
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-red-900 dark:text-red-100 mb-2">
              Upload Error
            </h3>
            <p className="text-red-700 dark:text-red-300 mb-4">
              {errorMessage}
            </p>
            <button
              onClick={() => {
                setUploadStatus('idle');
                setSelectedFile(null);
                setErrorMessage('');
              }}
              className="btn-secondary"
            >
              Try Again
            </button>
          </div>
        )}
      </div>

      {/* Trial Warning */}
      {!canCreateFlipbook && (
        <div className="mt-4 p-4 bg-accent/10 border border-accent rounded-lg">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-accent mr-2" />
            <p className="text-sm text-gray-700 dark:text-gray-300">
              You've reached your trial limit of {trialData.maxFlipbooks} flipbooks. 
              <button
                onClick={onUploadStart}
                className="text-primary hover:underline ml-1"
              >
                Upgrade to continue
              </button>
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default PDFUploader;
