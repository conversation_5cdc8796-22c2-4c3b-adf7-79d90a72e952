/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: '#6C63FF',
        accent: '#FFC857',
        background: '#F9F9F9',
        foreground: '#1A1A1A',
        border: '#E5E7EB',
        input: '#FFFFFF',
        ring: '#6C63FF',
        muted: '#F3F4F6',
        'muted-foreground': '#6B7280',
        dark: {
          primary: '#5A52E5',
          accent: '#FFB84D',
          background: '#1A1A1A',
          foreground: '#E5E5E5',
          border: '#374151',
          input: '#2D2D2D',
          ring: '#5A52E5',
          muted: '#2D2D2D',
          'muted-foreground': '#9CA3AF',
          surface: '#2D2D2D',
          text: '#E5E5E5'
        }
      },
      fontFamily: {
        'poppins': ['Poppins', 'sans-serif'],
      },
      animation: {
        'flip': 'flip 0.6s ease-in-out',
        'page-turn': 'pageTurn 0.8s ease-in-out',
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'slide-up': 'slideUp 0.4s ease-out',
      },
      keyframes: {
        flip: {
          '0%': { transform: 'rotateY(0deg)' },
          '50%': { transform: 'rotateY(-90deg)' },
          '100%': { transform: 'rotateY(0deg)' }
        },
        pageTurn: {
          '0%': { transform: 'rotateY(0deg) scale(1)' },
          '50%': { transform: 'rotateY(-90deg) scale(0.95)' },
          '100%': { transform: 'rotateY(-180deg) scale(1)' }
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' }
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' }
        }
      }
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
  darkMode: 'class',
}
