{"name": "flipnest", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.7", "@tailwindcss/typography": "^0.5.16", "@types/react-pdf": "^6.2.0", "@types/react-router-dom": "^5.3.3", "autoprefixer": "^10.4.21", "framer-motion": "^12.23.12", "lucide-react": "^0.534.0", "pdf-lib": "^1.17.1", "pdfjs-dist": "^5.4.54", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hotkeys-hook": "^5.1.0", "react-pdf": "^10.0.1", "react-router-dom": "^7.7.1", "tailwindcss": "^4.1.11", "turn.js": "^1.0.5"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}